---
import "../css/sanphamvadanhmuc.css";
import type { SanPhamVaDanhMucData } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: SanPhamVaDanhMucData;
}

const { data } = Astro.props;

// Use data from props or provide fallback
const products = data?.products || [];
const categories = data?.categories || [];

// Sắp xếp danh mục theo order
const displayCategories = categories
  .filter((category) => category.is_active)
  .sort((a, b) => {
    // Nếu một trong hai có order = 0, đưa về cuối
    if (a.order === 0 && b.order !== 0) return 1;
    if (b.order === 0 && a.order !== 0) return -1;
    // Nếu cả hai đều = 0 hoặc cả hai đều > 0, sort bình thường
    return a.order - b.order;
  })
  .slice(0, 6);

// Group products by category
const productsByCategory = products
  .filter((product) => product.category_id && product.is_active)
  .reduce(
    (acc, product) => {
      const categoryId = product.category_id.toString();
      if (!acc[categoryId]) {
        acc[categoryId] = [];
      }
      acc[categoryId].push(product);
      return acc;
    },
    {} as Record<string, typeof products>
  );

// Sort products trong mỗi category theo order và lấy 6 sản phẩm đầu
Object.keys(productsByCategory).forEach((categoryId) => {
  productsByCategory[categoryId] = productsByCategory[categoryId]
    .filter((product) => product.is_featured || product.is_active)
    .sort((a, b) => a.order - b.order)
    .slice(0, 6);
});
---

<section>
  <div class="product-background-section">
    <!-- Categories Section -->
    <div class="product-categories">
      {
        displayCategories.map((category, index) => (
          <div
            class={`product-category-item ${index === 0 ? "active" : ""}`}
            data-category={category.id}
          >
            {category.name}
          </div>
        ))
      }
      <div class="product-arrow-indicator">
        <svg
          viewBox="0 0 280 49"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
        >
          <path
            d="M0.99115 47.7851C33.5425 36.6978 44.8866 26.7367 48.5664 0H235.894C235.006 26.6738 243.739 37.7801 280 49H224.991H0L0.99115 47.7851Z"
            fill="#007bff"
          >
          </path>
        </svg>
      </div>
    </div>

    <!-- Products Section -->
    <div class="product-showcase-wrapper">
      {
        displayCategories.map((category, categoryIndex) => {
          const categoryProducts =
            productsByCategory[category.id.toString()] || [];
          return (
            <div
              class={`product-showcase-parent ${categoryIndex === 0 ? "active" : ""}`}
              data-category={category.id}
            >
              {[1, 2, 3, 4, 5, 6].map((position) => {
                const product = categoryProducts[position - 1];
                if (!product) {
                  return (
                    <div class={`product-showcase-div${position}`}>
                      <div class="empty-product-slot">
                        <i class="fa-solid fa-plus" />
                        <p>Chưa có sản phẩm</p>
                      </div>
                    </div>
                  );
                }

                return (
                  <div class={`product-showcase-div${position}`}>
                    <a href={`/san-pham/${product.id}`}>
                      <img
                        src={product.image_url}
                        alt={product.image_alt || product.name}
                      />
                      <div class="product-card-title">
                        <p>{product.name}</p>
                      </div>
                      <div class="product-card-hover">
                        <p>{product.description}</p>
                        {product.price_range && (
                          <p>Giá: {product.price_range}</p>
                        )}
                      </div>
                    </a>
                  </div>
                );
              })}
            </div>
          );
        })
      }
    </div>
  </div>
</section>

<script src="/js/sanphamvadanhmuc.js" defer></script>

<style>
  .empty-product-slot {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #9ca3af;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 20px;
  }

  .empty-product-slot i {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .empty-product-slot p {
    font-size: 12px;
    text-align: center;
    margin: 0;
  }

  .product-showcase-parent {
    display: none;
  }

  .product-showcase-parent.active {
    display: grid;
  }
</style>
