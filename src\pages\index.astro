---
import Layout from "../layouts/Layout.astro";
import "../css/banner.css";

// Import all components
import Banner from "../components/Banner.astro";
import G<PERSON>ith<PERSON> from "../components/gioithieu.astro";
import Duannoithat from "../components/duannoithat.astro";
import Sanphamvadanhmuc from "../components/sanphamvadanhmuc.astro";
import A13nam from "../components/a13nam.astro";
import Tttt from "../components/tttt.astro";
import Hesinhthai from "../components/hesinhthai.astro";
import Doitac from "../components/doitac.astro";
import Nbtct from "../components/nbtct.astro";

// Import API functions and types
import {
  fetchHomepageData,
  getFallbackData,
  getDataOrFallback,
  getMediaDataOrFallback,
  isValidData,
} from "../lib/api/homepage";

// Fetch all homepage data at build time
const homepageData = await fetchHomepageData();
const fallbackData = getFallbackData();

// Extract data for SEO and components with proper fallback handling
const bannersData = getDataOrFallback(
  homepageData.banners,
  fallbackData.banners.data
);

const gioithieuData = isValidData(homepageData.gioithieu)
  ? homepageData.gioithieu!.data
  : undefined; // Pass undefined instead of malformed fallback

const duannoithatData = getDataOrFallback(
  homepageData.duannoithat,
  fallbackData.duannoithat.data
);

const sanphamvadanhmucData = getDataOrFallback(
  homepageData.sanphamvadanhmuc,
  fallbackData.sanphamvadanhmuc.data
);

const a13namData = isValidData(homepageData.a13nam)
  ? homepageData.a13nam!.data
  : undefined; // Pass undefined instead of malformed fallback

const ttttData = getMediaDataOrFallback(
  homepageData.tttt,
  fallbackData.tttt.data
);

const hesinhthaiData = getDataOrFallback(
  homepageData.hesinhthai,
  fallbackData.hesinhthai.data
);

const doitacData = getDataOrFallback(
  homepageData.doitac,
  fallbackData.doitac.data
);

const nbtctData = getDataOrFallback(
  homepageData.nbtct,
  fallbackData.nbtct.data
);

// Enhanced SEO Props cho trang chủ using API data
const companyName =
  a13namData?.company_info?.company_name || "Công Ty Nội Thất";
const yearsExperience = a13namData?.company_info?.years_experience || 13;
const companyDescription =
  gioithieuData?.intro?.description ||
  "Chuyên thiết kế và thi công nội thất cao cấp với kinh nghiệm nhiều năm";
const ogImage =
  bannersData?.banners?.[0]?.image_url || "/images/og-homepage.jpg";
const homePageSEO = {
  title: `Thiết Kế Nội Thất Cao Cấp - Trang Chủ | ${companyName}`,
  description: `Chuyên thiết kế và thi công nội thất cao cấp với ${yearsExperience} năm kinh nghiệm. Tư vấn miễn phí, báo giá nhanh chóng. Đội ngũ kiến trúc sư chuyên nghiệp.`,
  keywords:
    "thiết kế nội thất, nội thất cao cấp, thi công nội thất, tư vấn nội thất, kiến trúc sư, nội thất hiện đại",
  canonical: Astro.url.href,
  ogTitle: `Thiết Kế Nội Thất Cao Cấp - Giải Pháp Hoàn Hảo Cho Ngôi Nhà | ${companyName}`,
  ogDescription: companyDescription,
  ogImage: ogImage,
  imageAlt: "Thiết kế nội thất cao cấp - Trang chủ",
  robots: "index, follow",
  author: companyName,
  schemaJson: JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Organization",
    name: companyName,
    url: Astro.url.origin,
    logo: {
      "@type": "ImageObject",
      url: `${Astro.url.origin}/images/logo.png`,
      width: 200,
      height: 60,
    },
    description: companyDescription,
    foundingDate: new Date().getFullYear() - yearsExperience,
    address: {
      "@type": "PostalAddress",
      addressCountry: "VN",
      addressLocality: "Hồ Chí Minh",
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+84-xxx-xxx-xxx",
      contactType: "customer service",
    },
    sameAs: ["https://facebook.com/yourpage", "https://instagram.com/yourpage"],
    potentialAction: {
      "@type": "SearchAction",
      target: `${Astro.url.origin}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  }),
};
---

<Layout {...homePageSEO}>
  <!-- Phần cần lazy load -->
  <div id="lazy-content">
    <Banner data={bannersData} />
    <Gioithieu data={gioithieuData} />
    <Duannoithat data={duannoithatData} />
    <Sanphamvadanhmuc data={sanphamvadanhmucData} />
    <Nbtct data={nbtctData} />
    <A13nam data={a13namData} />
    <Tttt data={ttttData} />
    <Hesinhthai data={hesinhthaiData} />
    <Doitac data={doitacData} />
  </div>
  <script src="/js/index.js" defer></script>
</Layout>
