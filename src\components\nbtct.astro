---
import "../css/nbtct.css";
import type { NbtctData } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: NbtctData;
}

const { data } = Astro.props;

// Use data from props
const brandIdentity = data?.brand_identity;
---

{
  brandIdentity && (
    <section
      class="statistics-section"
      style={
        brandIdentity.image_url
          ? `background-image: url('${brandIdentity.image_url}')`
          : "background-image: url('../images/image-11.png')"
      }
    >
      {brandIdentity.image_url && (
        <div class="background-image-overlay">
          <img
            src={brandIdentity.image_url}
            alt={brandIdentity.image_alt || "Nhận biết thương hiệu"}
            class="hidden-bg-img"
          />
        </div>
      )}

      <div class="stats-container">
        <div class="stats-section-header stats-text-center">
          <h2 class="stats-section-title">{brandIdentity.title}</h2>
          <p class="stats-section-description">{brandIdentity.description}</p>
        </div>

        {/* Brand Elements */}
        {brandIdentity.brand_elements && (
          <div class="brand-elements-section">
            {brandIdentity.brand_elements.colors &&
              brandIdentity.brand_elements.colors.length > 0 && (
                <div class="brand-colors">
                  <h3>Màu sắc thương hiệu:</h3>
                  <div class="color-palette">
                    {brandIdentity.brand_elements.colors.map((color) => (
                      <div
                        class="color-item"
                        style={`background-color: ${color}`}
                        title={color}
                      />
                    ))}
                  </div>
                </div>
              )}

            {brandIdentity.brand_elements.values &&
              brandIdentity.brand_elements.values.length > 0 && (
                <div class="brand-values">
                  <h3>Giá trị cốt lõi:</h3>
                  <div class="values-grid">
                    {brandIdentity.brand_elements.values.map((value) => (
                      <div class="value-item">
                        <i class="fas fa-star" />
                        <span>{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>
        )}

        {/* Unique Selling Points */}
        {brandIdentity.unique_selling_points &&
          brandIdentity.unique_selling_points.length > 0 && (
            <div class="usp-section">
              <h3>Điểm khác biệt:</h3>
              <div class="usp-grid">
                {brandIdentity.unique_selling_points.map((usp, index) => (
                  <div class="usp-card">
                    <div class="usp-number">{index + 1}</div>
                    <p>{usp}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
      </div>
    </section>
  )
}
<script src="/js/nbtct.js" defer></script>
