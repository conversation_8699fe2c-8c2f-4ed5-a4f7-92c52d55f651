---
import "../css/a13nam.css";
// Import types from the types directory
import type { A13NamData } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: A13NamData;
}

const { data } = Astro.props;

// Use data from props
const companyInfo = data?.company_info;
---

{companyInfo && (
  <section class="experience-section">
    <div class="container">
      <div class="experience-grid">
        <div class="experience-content">
          <div class="experience-number">{companyInfo.years_experience}</div>
          <h2 class="experience-title">{companyInfo.company_name}</h2>
          <p class="experience-description">
            {companyInfo.description}
          </p>
          
          {companyInfo.achievements && companyInfo.achievements.length > 0 && (
            <div class="achievements-list">
              <h3>Thành tựu nổi bật:</h3>
              <ul>
                {companyInfo.achievements.map(achievement => (
                  <li>{achievement}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div class="experience-video">
          {companyInfo.image_url ? (
            <div class="company-image">
              <img 
                src={companyInfo.image_url}
                alt={companyInfo.image_alt || `Hình ảnh ${companyInfo.company_name}`}
                class="company-image-img"
              />
            </div>
          ) : (
            <div class="image-placeholder">
              <div class="no-image-message">
                <i class="fa-solid fa-building" style="font-size: 48px; color: rgba(0,0,0,0.3); margin-bottom: 8px;"></i>
                <p style="color: rgba(0,0,0,0.5); font-size: 14px; margin: 0;">Hình ảnh công ty</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Company Stats */}
      <div class="company-stats">
        <div class="stat-item">
          <div class="stat-number">{companyInfo.years_experience}</div>
          <div class="stat-label">Năm kinh nghiệm</div>
        </div>
        
        {companyInfo.projects_completed && (
          <div class="stat-item">
            <div class="stat-number">{companyInfo.projects_completed}</div>
            <div class="stat-label">Dự án hoàn thành</div>
          </div>
        )}
        
        {companyInfo.team_size && (
          <div class="stat-item">
            <div class="stat-number">{companyInfo.team_size}</div>
            <div class="stat-label">Thành viên</div>
          </div>
        )}
        
        {companyInfo.awards && companyInfo.awards.length > 0 && (
          <div class="stat-item">
            <div class="stat-number">{companyInfo.awards.length}</div>
            <div class="stat-label">Giải thưởng</div>
          </div>
        )}
      </div>

      {/* Certifications */}
      {companyInfo.certifications && companyInfo.certifications.length > 0 && (
        <div class="certifications-section">
          <h3>Chứng nhận & Giấy phép:</h3>
          <div class="certifications-grid">
            {companyInfo.certifications.map(cert => (
              <div class="certification-item">
                <i class="fa-solid fa-certificate"></i>
                <span>{cert}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  </section>
)}

<script src="/js/a13nam.js" defer is:inline></script>

<style>
  .company-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    border-radius: clamp(6px, 1vw, 12px);
    overflow: hidden;
  }
  
  .company-image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: clamp(6px, 1vw, 12px);
  }
  
  .image-placeholder {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    border-radius: clamp(6px, 1vw, 12px);
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #d1d5db;
  }
  
  .no-image-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  
  .company-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 12px;
  }
  
  .stat-item {
    text-align: center;
  }
  
  .stat-number {
    font-size: 3rem;
    font-weight: bold;
    color: #1e40af;
    margin-bottom: 0.5rem;
  }
  
  .stat-label {
    font-size: 1rem;
    color: #64748b;
    font-weight: 500;
  }
  
  .achievements-list {
    margin-top: 1.5rem;
  }
  
  .achievements-list h3 {
    color: #1e40af;
    margin-bottom: 1rem;
  }
  
  .achievements-list ul {
    list-style: none;
    padding: 0;
  }
  
  .achievements-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
    position: relative;
    padding-left: 1.5rem;
  }
  
  .achievements-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
  }
  
  .certifications-section {
    margin-top: 3rem;
  }
  
  .certifications-section h3 {
    color: #1e40af;
    margin-bottom: 1.5rem;
  }
  
  .certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .certification-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
  }
  
  .certification-item:hover {
    border-color: #1e40af;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.1);
  }
  
  .certification-item i {
    color: #f59e0b;
    font-size: 1.25rem;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .company-stats {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
      padding: 1.5rem;
    }
    
    .stat-number {
      font-size: 2rem;
    }
    
    .certifications-grid {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 480px) {
    .company-stats {
      grid-template-columns: 1fr;
    }
  }
</style>
