---
import Layout from "../layouts/Layout.astro";
import "../css/banner.css";

// Import all components
import Banner from "../components/Banner.astro";
import <PERSON><PERSON><PERSON><PERSON> from "../components/gioithieu.astro";
import Duannoithat from "../components/duannoithat.astro";
import Sanphamvadanhmuc from "../components/sanphamvadanhmuc.astro";
import A13nam from "../components/a13nam.astro";
import Tttt from "../components/tttt.astro";
import Hesinhthai from "../components/hesinhthai.astro";
import Doitac from "../components/doitac.astro";
import Nbtct from "../components/nbtct.astro";

// Import mock data
import {
  mockHomepageData,
  mockBannerData,
  mockGioiThieuData,
  mockDuAnNoiThatData,
  mockSanPhamVaDanhMucData,
  mockA13NamData,
  mockMediaData,
  mockHeSinhThaiData,
  mockDoiTacData,
  mockNbtctData,
} from "../lib/mockData";

// Use mock data for demo
const bannersData = mockBannerData;
const gioithieuData = mockGioiThieuData;
const duannoithatData = mockDuAnNoiThatData;
const sanphamvadanhmucData = mockSanPhamVaDanhMucData;
const a13namData = mockA13NamData;
const ttttData = mockMediaData;
const hesinhthaiData = mockHeSinhThaiData;
const doitacData = mockDoiTacData;
const nbtctData = mockNbtctData;

// SEO data for demo page
const demoPageSEO = {
  title: "Demo - Trang chủ nội thất A13",
  description: "Trang demo hiển thị các components với dữ liệu mẫu - Thiết kế nội thất cao cấp, chuyên nghiệp với 13 năm kinh nghiệm",
  canonical: "/demo",
  ogTitle: "Demo - Nội thất A13 - Thiết kế nội thất cao cấp",
  ogDescription: "Xem trước giao diện trang chủ với dữ liệu mẫu - Chuyên thiết kế và thi công nội thất",
  ogImage: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
  ogUrl: "/demo",
  twitterCard: "summary_large_image",
  twitterTitle: "Demo - Nội thất A13",
  twitterDescription: "Trang demo hiển thị giao diện với dữ liệu mẫu",
  twitterImage: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
  keywords: "demo, nội thất, thiết kế, A13, mock data, preview",
  author: "Nội thất A13",
  publishedTime: new Date().toISOString(),
  modifiedTime: new Date().toISOString(),
  ogType: "website"
};
---

<Layout {...demoPageSEO}>
  <!-- Demo Notice -->
  <div style="background: #fef3c7; padding: 1rem; text-align: center; border-bottom: 2px solid #f59e0b;">
    <p style="margin: 0; color: #92400e; font-weight: 600;">
      🚧 TRANG DEMO - Hiển thị với dữ liệu mẫu để xem trước giao diện
    </p>
  </div>

  <!-- Main content with mock data -->
  <div id="demo-content">
    <Banner data={bannersData} />
    <Gioithieu data={gioithieuData} />
    <Duannoithat data={duannoithatData} />
    <Sanphamvadanhmuc data={sanphamvadanhmucData} />
    <Nbtct data={nbtctData} />
    <A13nam data={a13namData} />
    <Tttt data={ttttData} />
    <Hesinhthai data={hesinhthaiData} />
    <Doitac data={doitacData} />
  </div>

  <!-- Demo info section -->
  <section style="background: #f3f4f6; padding: 3rem 1rem; margin-top: 2rem;">
    <div style="max-width: 1200px; margin: 0 auto; text-align: center;">
      <h2 style="color: #1f2937; margin-bottom: 1rem;">Thông tin Demo</h2>
      <p style="color: #6b7280; margin-bottom: 2rem; max-width: 600px; margin-left: auto; margin-right: auto;">
        Trang này hiển thị tất cả các components với dữ liệu mẫu (mock data) để bạn có thể xem trước giao diện 
        mà không cần kết nối API thật. Tất cả hình ảnh và nội dung đều là dữ liệu demo.
      </p>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-top: 2rem;">
        <div style="background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h3 style="color: #1f2937; margin-bottom: 0.5rem;">📊 Components</h3>
          <p style="color: #6b7280; font-size: 0.9rem;">9 components được hiển thị với dữ liệu mẫu</p>
        </div>
        
        <div style="background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h3 style="color: #1f2937; margin-bottom: 0.5rem;">🎨 Mock Data</h3>
          <p style="color: #6b7280; font-size: 0.9rem;">Dữ liệu mẫu được tạo trong src/lib/mockData.ts</p>
        </div>
        
        <div style="background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h3 style="color: #1f2937; margin-bottom: 0.5rem;">🔗 Type Safe</h3>
          <p style="color: #6b7280; font-size: 0.9rem;">Sử dụng TypeScript interfaces từ src/types/index.ts</p>
        </div>
      </div>
      
      <div style="margin-top: 2rem;">
        <a href="/" style="display: inline-block; background: #3b82f6; color: white; padding: 0.75rem 1.5rem; border-radius: 6px; text-decoration: none; font-weight: 500;">
          ← Quay lại trang chủ thật
        </a>
      </div>
    </div>
  </section>

  <script src="/js/index.js" defer is:inline></script>
</Layout>

<style>
  /* Demo specific styles */
  #demo-content {
    position: relative;
  }
  
  #demo-content::before {
    content: "DEMO MODE";
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }
  
  @media (max-width: 768px) {
    #demo-content::before {
      top: 10px;
      right: 10px;
      font-size: 0.7rem;
      padding: 0.4rem 0.8rem;
    }
  }
</style>
