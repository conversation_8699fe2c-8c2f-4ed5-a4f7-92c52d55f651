* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Inter", sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Experience Section */
.experience-section {
    padding: clamp(32px, 8vw, 64px) 0;
    background-color: #f3f4f6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 clamp(16px, 4vw, 32px);
}

.experience-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: clamp(24px, 6vw, 48px);
    align-items: center;
}

.experience-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(16px, 3vw, 24px);
}

.experience-number {
    font-family: 'Me<PERSON><PERSON> Script', Regular;
    font-size: clamp(48px, 8vw, 96px);
    font-weight: bold;
    color: #1f2937;
    line-height: 1;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.experience-title {
    font-size: clamp(24px, 5vw, 48px);
    font-weight: bold;
    color: #1f2937;
    margin: 0;
    line-height: 1.2;
}

.experience-description {
    color: #6b7280;
    font-size: clamp(14px, 1.2vw, 16px);
    line-height: 1.6;
    margin: 0;
    max-width: 600px;
}

/* Video Section */
.experience-video {
    position: relative;
    border-radius: clamp(6px, 1vw, 12px);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.experience-video iframe {
    display: block;
    border-radius: clamp(6px, 1vw, 12px);
}

.video-thumbnail {
    position: relative;
    background-color: #f0f0f0;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.02);
}

.video-overlay-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: clamp(6px, 1vw, 12px);
    transition: background-color 0.3s ease;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.video-overlay-wrapper:hover {
    background-color: rgba(0, 0, 0, 0.6);
}

.video-play-btn {
    background-color: #dc2626;
    border: none;
    border-radius: 50%;
    width: clamp(44px, 6vw, 72px);
    height: clamp(44px, 6vw, 72px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 44px;
    min-height: 44px;
}

.video-play-btn:hover {
    background-color: #b91c1c;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.video-play-btn svg {
    width: clamp(20px, 3vw, 36px);
    height: clamp(20px, 3vw, 36px);
    color: white;
    margin-left: clamp(2px, 0.3vw, 4px);
}

/* Horizontal Cards Section */
.horizontal-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: clamp(16px, 4vw, 24px);
    margin-top: clamp(32px, 6vw, 48px);
}

/* Optimized Card Design */
.card-horizontal {
    background: white;
    border-radius: clamp(8px, 2vw, 15px);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    transform: translateY(30px);
}

.card-horizontal.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.card-horizontal-image {
    position: relative;
    height: clamp(150px, 18vw, 200px);
    overflow: hidden;
}

.card-horizontal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.card-horizontal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(46, 204, 113, 0.8) 50%, rgba(39, 174, 96, 0.8) 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
    transform: scaleX(0);
    transform-origin: left;
}

.card-horizontal-title {
    color: white;
    font-size: clamp(14px, 1.5vw, 18px);
    font-weight: 600;
    text-align: center;
    padding: 0 clamp(8px, 2vw, 16px);
    opacity: 0.9;
    transition: all 0.4s ease;
    line-height: 1.3;
}

.card-horizontal-description {
    padding: clamp(12px, 3vw, 20px);
    color: #6b7280;
    line-height: 1.6;
    font-size: clamp(12px, 1.2vw, 14px);
}

/* Card Hover Effects */
.card-horizontal:hover .card-horizontal-overlay {
    transform: scaleX(1);
}

.card-horizontal:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-horizontal:hover .card-horizontal-image img {
    filter: grayscale(100%) contrast(120%);
    transform: scale(1.08);
}

.card-horizontal:hover .card-horizontal-title {
    transform: scale(1.05);
}

/* Card Color Variations */
.card-horizontal:nth-child(1) .card-horizontal-overlay {
    background: linear-gradient(90deg, rgba(52, 152, 219, 0.8) 50%, rgba(41, 128, 185, 0.8) 50%);
}

.card-horizontal:nth-child(1):hover {
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.card-horizontal:nth-child(2) .card-horizontal-overlay {
    background: linear-gradient(90deg, rgba(231, 76, 60, 0.8) 50%, rgba(192, 57, 43, 0.8) 50%);
}

.card-horizontal:nth-child(2):hover {
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.card-horizontal:nth-child(3) .card-horizontal-overlay {
    background: linear-gradient(90deg, rgba(155, 89, 182, 0.8) 50%, rgba(142, 68, 173, 0.8) 50%);
}

.card-horizontal:nth-child(3):hover {
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);
}

.card-horizontal:nth-child(4) .card-horizontal-overlay {
    background: linear-gradient(90deg, rgba(230, 126, 34, 0.8) 50%, rgba(211, 84, 0, 0.8) 50%);
}

.card-horizontal:nth-child(4):hover {
    box-shadow: 0 8px 25px rgba(230, 126, 34, 0.3);
}

/* Animation Delays */
.card-horizontal[data-aos-delay="100"] {
    transition-delay: 0.1s;
}

.card-horizontal[data-aos-delay="200"] {
    transition-delay: 0.2s;
}

.card-horizontal[data-aos-delay="300"] {
    transition-delay: 0.3s;
}

.card-horizontal[data-aos-delay="400"] {
    transition-delay: 0.4s;
}

/* Responsive Design */
@media (min-width: 1200px) {
    .experience-grid {
        grid-template-columns: 1fr 1fr;
        gap: 64px;
    }

    .experience-content {
        max-width: 500px;
    }

    .horizontal-cards-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .experience-grid {
        grid-template-columns: 1fr 1fr;
        gap: 32px;
    }

    .horizontal-cards-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .experience-content {
        text-align: center;
        order: 1;
    }

    .experience-video {
        order: 2;
    }

    .horizontal-cards-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-top: 24px;
    }

    .card-horizontal-image {
        height: clamp(120px, 15vw, 160px);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 clamp(12px, 3vw, 20px);
    }

    .card-horizontal-description {
        padding: clamp(10px, 2.5vw, 16px);
    }
}

/* Mobile landscape optimization */
@media (max-width: 768px) and (orientation: landscape) and (max-height: 500px) {
    .experience-section {
        padding: 16px 0;
    }

    .experience-grid {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .experience-content {
        text-align: left;
        gap: 8px;
    }

    .horizontal-cards-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-top: 16px;
    }

    .card-horizontal-image {
        height: clamp(80px, 12vw, 120px);
    }
}

/* Animations */
.experience-content {
    opacity: 0;
    transform: translateX(-20px);
    animation: fadeInLeft 0.8s ease forwards;
}

.experience-video {
    opacity: 0;
    transform: translateX(20px);
    animation: fadeInRight 0.8s ease 0.2s forwards;
}

.card-horizontal {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile animations - simplified for performance */
@media (max-width: 767px) {

    .experience-content,
    .experience-video {
        animation: fadeInUp 0.6s ease forwards;
    }

    .experience-video {
        animation-delay: 0.2s;
    }
}

/* Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
        opacity: 1 !important;
    }
}

/* Print styles */
@media print {
    .experience-section {
        padding: 20px 0;
        background: white;
    }

    .video-overlay-wrapper {
        display: none;
    }

    .card-horizontal-overlay {
        display: none;
    }

    .horizontal-cards-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .video-play-btn {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    }
}