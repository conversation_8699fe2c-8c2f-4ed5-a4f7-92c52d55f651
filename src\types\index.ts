// Common API Response Structure
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    generated_at?: string;
    [key: string]: any;
  };
  message?: string;
}

// Banner Types
export interface Banner {
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  image_url: string;
  image_alt?: string;
  link_url?: string;
  button_text?: string;
  order: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface BannerData {
  banners: Banner[];
}

// Gioi Thieu Types
export interface GioiThieu {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  content: string;
  extended_description?: string;
  video_id?: string;
  image_url?: string;
  image_alt?: string;
  stats?: {
    years_experience?: number;
    projects_completed?: number;
    happy_customers?: number;
    awards?: number;
  };
  features?: string[];
  is_active: boolean;
}

export interface GioiThieuData {
  intro: GioiThieu;
}

// Du An Noi That Types
export interface DuAnNoiThat {
  id: number;
  title: string;
  description?: string;
  category: string;
  image_url: string;
  image_alt?: string;
  gallery?: string[];
  area?: number;
  location?: string;
  completion_date?: string;
  budget_range?: string;
  style?: string;
  order: number;
  is_featured: boolean;
  is_active: boolean;
}

export interface DuAnNoiThatData {
  projects: DuAnNoiThat[];
  categories: string[];
}

// San Pham Va Danh Muc Types
export interface SanPham {
  id: number;
  name: string;
  description?: string;
  category_id: number;
  category_name: string;
  price?: number;
  price_range?: string;
  image_url: string;
  image_alt?: string;
  gallery?: string[];
  specifications?: Record<string, any>;
  is_featured: boolean;
  is_active: boolean;
  order: number;
}

export interface DanhMuc {
  id: number;
  name: string;
  description?: string;
  image_url?: string;
  image_alt?: string;
  parent_id?: number;
  order: number;
  is_active: boolean;
  product_count?: number;
}

export interface SanPhamVaDanhMucData {
  products: SanPham[];
  categories: DanhMuc[];
}

// A13Nam (Company Info) Types
export interface CompanyInfo {
  id: number;
  company_name: string;
  years_experience: number;
  description: string;
  achievements: string[];
  certifications?: string[];
  team_size?: number;
  projects_completed?: number;
  awards?: string[];
  image_url?: string;
  image_alt?: string;
}

export interface A13NamData {
  company_info: CompanyInfo;
}

// Media Content Types (TTTT)
export interface MediaItem {
  id: number;
  title: string;
  type: string;
  section: string;
  embed_code: string;
  media_url: string;
  media_id: string;
  thumbnail_url: string | null;
  thumbnail_alt: string | null;
  thu_tu: number;
  is_youtube: boolean;
  is_tiktok: boolean;
}

export interface MediaData {
  video_section: MediaItem[];
  tiktok_section: MediaItem[];
}

export interface MediaMeta {
  total_videos: number;
  total_tiktoks: number;
  total_items: number;
  generated_at: string;
}

export interface MediaApiResponse {
  success: boolean;
  data: MediaData;
  meta: MediaMeta;
}

// He Sinh Thai Types
export interface HeSinhThai {
  id: number;
  title: string;
  description: string;
  services: string[];
  process_steps?: {
    step: number;
    title: string;
    description: string;
    icon?: string;
  }[];
  benefits?: string[];
  image_url?: string;
  image_alt?: string;
}

export interface HeSinhThaiData {
  ecosystem: HeSinhThai;
}

// Doi Tac Types
export interface DoiTac {
  id: number;
  name: string;
  logo_url: string;
  logo_alt?: string;
  website_url?: string;
  description?: string;
  partnership_type: string;
  order: number;
  is_active: boolean;
}

export interface DoiTacData {
  partners: DoiTac[];
}

// Nhan Biet Thuong Hieu Types
export interface NhanBietThuongHieu {
  id: number;
  title: string;
  description: string;
  brand_elements: {
    logo?: string;
    colors?: string[];
    fonts?: string[];
    values?: string[];
  };
  unique_selling_points: string[];
  image_url?: string;
  image_alt?: string;
}

export interface NbtctData {
  brand_identity: NhanBietThuongHieu;
}

// Combined Homepage Data
export interface HomepageData {
  banners: BannerData;
  gioithieu: GioiThieuData;
  duannoithat: DuAnNoiThatData;
  sanphamvadanhmuc: SanPhamVaDanhMucData;
  a13nam: A13NamData;
  tttt: MediaApiResponse;
  hesinhthai: HeSinhThaiData;
  doitac: DoiTacData;
  nbtct: NbtctData;
}

// API Endpoints
export const API_ENDPOINTS = {
  BANNERS: "/api/banners",
  GIOITHIEU: "/api/gioithieu",
  DUANNOITHAT: "/api/duannoithat",
  SANPHAMVADANHMUC: "/api/sanphamvadanhmuc",
  A13NAM: "/api/a13nam",
  TTTT: "/api/media-contents",
  HESINHTHAI: "/api/hesinhthai",
  DOITAC: "/api/doitac",
  NBTCT: "/api/nbtct",
} as const;

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error?: ApiError | null;
}
