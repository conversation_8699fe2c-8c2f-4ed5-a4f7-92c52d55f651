---
import BgdichVu from "../../components/dich-vu/bgdich-vu.astro";
import NddichVu from "../../components/dich-vu/nddich-vu.astro";
import Layout from "../../layouts/Layout.astro";

// Fetch SEO data cho trang dịch vụ
let seoData = null;
let pageTitle = "Dịch vụ thiết kế nội thất";
let pageDescription = "Khám phá các dịch vụ thiết kế nội thất chuyên nghiệp của chúng tôi";

try {
  const response = await fetch(`${import.meta.env.PUBLIC_API_BASE}/api/pages/trangdichvu`);
  if (response.ok) {
    const result = await response.json();
    if (result.success) {
      seoData = result.data;
      pageTitle = seoData.meta_title || pageTitle;
      pageDescription = seoData.meta_description || pageDescription;
    }
  }
} catch (error) {
  console.error("Failed to fetch SEO data:", error);
}
---

<Layout 
  title={seoData?.meta_title || pageTitle}
  description={seoData?.meta_description || pageDescription}
  canonical={seoData?.canonical_url}
  ogTitle={seoData?.og_title}
  ogDescription={seoData?.og_description}
  ogImage={seoData?.og_image}
  imageAlt={seoData?.image_alt}
  robots={seoData?.robots}
>
  <BgdichVu />
  <NddichVu />
</Layout>