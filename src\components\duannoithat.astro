---
import "../css/duannoithat.css";
import { Image } from "astro:assets";
import type { DuAnNoiThatData } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: DuAnNoiThatData;
}

const { data } = Astro.props;

// Use data from props or provide fallback
const projects = data?.projects || [];
const categories = data?.categories || [];

// For now, we'll create a simple mapping for the existing layout
// You may need to adjust this based on your actual API structure
const featuredProjects = projects
  .filter((project) => project.is_featured)
  .slice(0, 6);

// Thứ tự hiển thị theo layout
const positions = [
  "service-div1",
  "service-div6",
  "service-div5",
  "service-div2",
  "service-div3",
  "service-div4",
];
---

<section class="services-section">
  {
    projects.length > 0 && (
      <div class="services-title">
        <h1>D<PERSON><PERSON></h1>
      </div>
    )
  }

  <div class="services-container">
    <div class="services-grid">
      {
        featuredProjects.map((project, index) => {
          const position = positions[index] || `service-div${index + 1}`;

          return (
            <div class={`service-card ${position}`}>
              <a href={`/du-an/${project.id}`}>
                <img
                  src={project.image_url}
                  alt={project.image_alt || project.title}
                  loading="lazy"
                  decoding="async"
                  width="800"
                  height="500"
                  fetchpriority="low"
                  class="w-full h-auto object-cover rounded-md"
                />
                <div class="service-title">
                  <p>{project.title}</p>
                </div>
                <div class="service-hover">
                  <p>{project.description}</p>
                  {project.location && <p>Địa điểm: {project.location}</p>}
                  {project.area && <p>Diện tích: {project.area}m²</p>}
                </div>
              </a>
            </div>
          );
        })
      }
    </div>

    <!-- Vertical slider with remaining projects -->
    {
      projects.length > 6 && (
        <div class="vertical-scroller">
          <div class="vertical-slider-wrapper">
            {projects.slice(6).map((project) => (
              <div class="service-card-x">
                <a href={`/du-an/${project.id}`}>
                  <img
                    src={project.image_url}
                    alt={project.image_alt || project.title}
                    loading="lazy"
                  />
                  <div class="service-title">
                    <p>{project.title}</p>
                  </div>
                  <div class="service-hover">
                    <p>{project.description}</p>
                    {project.category && <p>Danh mục: {project.category}</p>}
                  </div>
                </a>
              </div>
            ))}
          </div>
        </div>
      )
    }
  </div>
</section>
<script src="/js/duannoithat.js" defer></script>
