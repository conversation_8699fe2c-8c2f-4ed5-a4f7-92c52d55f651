# Component Migration Guide - SSR Pattern

## Tổng quan

Tài liệu này hướng dẫn cách chuyển đổi các components từ việc fetch API trực tiế<PERSON> sang nhận data từ props theo chuẩn SSR của Astro.

## Cấu trúc đã hoàn thành

### 1. Types Definition (`src/types/index.ts`)

- ✅ Đã định nghĩa tất cả interfaces cho API responses
- ✅ <PERSON>o gồm types cho tất cả components
- ✅ Có fallback data structure

### 2. API Service (`src/lib/api/homepage.ts`)

- ✅ Fetch tất cả data từ các API endpoints
- ✅ Error handling cho từng API call
- ✅ Concurrent fetching với Promise.all
- ✅ Fallback data khi API fails

### 3. Index Page (`src/pages/index.astro`)

- ✅ Fetch tất cả data tại build time
- ✅ Truyền data xuống các components
- ✅ Dynamic SEO based on API data
- ✅ Error handling và fallbacks

### 4. Components đã cập nhật

- ✅ `Banner.astro` - <PERSON><PERSON>ận BannerData từ props
- ✅ `Gioithieu.astro` - <PERSON>hận GioiThieuData từ props
- ✅ `Duannoithat.astro` - Nhận DuAnNoiThatData từ props
- ✅ `Tttt.astro` - Nhận MediaData từ props

## Components cần cập nhật

### 5. `Sanphamvadanhmuc.astro`

```typescript
// Cần thay đổi từ:
const products = await fetchProducts();
const categories = await fetchCategories();

// Thành:
export interface Props {
  data?: SanPhamVaDanhMucData;
}
const { data } = Astro.props;
const products = data?.products || [];
const categories = data?.categories || [];
```

### 6. `A13nam.astro`

```typescript
// Cần thay đổi từ:
const companyInfo = await fetchCompanyInfo();

// Thành:
export interface Props {
  data?: A13NamData;
}
const { data } = Astro.props;
const companyInfo = data?.company_info;
```

### 7. `Hesinhthai.astro`

```typescript
// Cần thay đổi từ:
const ecosystemData = await fetchEcosystem();

// Thành:
export interface Props {
  data?: HeSinhThaiData;
}
const { data } = Astro.props;
const ecosystemData = data?.ecosystem;
```

### 8. `Doitac.astro`

```typescript
// Cần thay đổi từ:
const partners = await fetchPartners();

// Thành:
export interface Props {
  data?: DoiTacData;
}
const { data } = Astro.props;
const partners = data?.partners || [];
```

### 9. `Nbtct.astro`

```typescript
// Cần thay đổi từ:
const brandIdentity = await fetchBrandIdentity();

// Thành:
export interface Props {
  data?: NbtctData;
}
const { data } = Astro.props;
const brandIdentity = data?.brand_identity;
```

## Quy trình cập nhật từng component

### Bước 1: Cập nhật import và props

```astro
---
// Xóa import API functions
// import { fetchData } from "../lib/api/...";

// Thêm import types
import type { [DataType] } from "../types/index";

// Thêm Props interface
export interface Props {
  data?: [DataType];
}

const { data } = Astro.props;

// Sử dụng data từ props
const componentData = data?.[property] || fallback;
---
```

### Bước 2: Cập nhật HTML template

- Thay thế các biến cũ bằng data từ props
- Thêm conditional rendering khi không có data
- Đảm bảo fallback content hiển thị phù hợp

### Bước 3: Test và debug

- Kiểm tra TypeScript errors
- Test với data có và không có
- Đảm bảo fallback hoạt động đúng

## API Endpoints mapping

```typescript
const API_ENDPOINTS = {
  BANNERS: "/api/banners", // → BannerData
  GIOITHIEU: "/api/gioithieu", // → GioiThieuData
  DUANNOITHAT: "/api/duannoithat", // → DuAnNoiThatData
  SANPHAMVADANHMUC: "/api/sanphamvadanhmuc", // → SanPhamVaDanhMucData
  A13NAM: "/api/a13nam", // → A13NamData
  TTTT: "/api/media-contents", // → MediaApiResponse
  HESINHTHAI: "/api/hesinhthai", // → HeSinhThaiData
  DOITAC: "/api/doitac", // → DoiTacData
  NBTCT: "/api/nbtct", // → NbtctData
};
```

## Lợi ích của pattern này

1. **Performance**: Tất cả data được fetch song song tại build time
2. **SEO**: Server-side rendering với data đầy đủ
3. **Error Handling**: Graceful fallbacks khi API fails
4. **Type Safety**: Full TypeScript support
5. **Maintainability**: Centralized data fetching
6. **Caching**: Build-time data caching

## Lưu ý quan trọng

1. **Fallback Content**: Luôn cung cấp fallback khi không có data
2. **Type Safety**: Sử dụng optional chaining (`?.`) khi truy cập properties
3. **Error Boundaries**: Components phải hoạt động ngay cả khi API fails
4. **Performance**: Tránh fetch data trong components, chỉ nhận từ props

## Template sử dụng

Sử dụng file `src/components/_template.astro` làm template cho các components mới.

## Kiểm tra hoàn thành

- [x] Sanphamvadanhmuc.astro ✅
- [x] A13nam.astro ✅
- [x] Hesinhthai.astro ✅
- [x] Doitac.astro ✅
- [x] Nbtct.astro ✅

Sau khi hoàn thành tất cả, website sẽ có:

- ✅ Full SSR support
- ✅ Optimized performance
- ✅ Better SEO
- ✅ Error resilience
- ✅ Type safety
