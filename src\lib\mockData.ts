import type {
  BannerData,
  GioiThieuData,
  DuAnNoiThatData,
  SanPhamVaDanhMucData,
  A13NamData,
  MediaApiResponse,
  HeSinhThaiData,
  DoiTacData,
  NbtctData,
} from "../types/index";

// Mock Banner Data
export const mockBannerData: BannerData = {
  banners: [
    {
      id: 1,
      title: "Thiết kế nội thất cao cấp",
      subtitle: "Tạo không gian sống hoàn hảo",
      description:
        "Chúng tôi mang đến những giải pháp thiết kế nội thất độc đáo, sang trọng và tiện nghi cho ngôi nhà của bạn.",
      image_url:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      image_alt: "Phòng khách hiện đại",
      link_url: "/dich-vu",
      button_text: "<PERSON>h<PERSON>m phá ngay",
      order: 1,
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    {
      id: 2,
      title: "Nội thất phòng ngủ sang trọng",
      subtitle: "Không gian nghỉ ngơi lý tưởng",
      description:
        "Thiết kế phòng ngủ với phong cách hiện đại, tối ưu hóa không gian và mang lại cảm giác thư giãn tuyệt đối.",
      image_url:
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      image_alt: "Phòng ngủ hiện đại",
      link_url: "/du-an",
      button_text: "Xem dự án",
      order: 2,
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
  ],
};

// Mock Gioi Thieu Data
export const mockGioiThieuData: GioiThieuData = {
  intro: {
    id: 1,
    title: "Về chúng tôi",
    subtitle: "Đơn vị thiết kế nội thất hàng đầu Việt Nam",
    description:
      "Với hơn 13 năm kinh nghiệm trong lĩnh vực thiết kế và thi công nội thất, chúng tôi đã tạo ra hàng nghìn không gian sống đẹp và tiện nghi cho khách hàng trên toàn quốc.",
    content:
      "Chúng tôi chuyên cung cấp các dịch vụ thiết kế nội thất toàn diện từ tư vấn, thiết kế 2D/3D đến thi công hoàn thiện.",
    extended_description:
      "Đội ngũ kiến trúc sư và thợ thi công giàu kinh nghiệm của chúng tôi luôn sẵn sàng biến ước mơ về không gian sống lý tưởng của bạn thành hiện thực. Chúng tôi cam kết mang đến chất lượng tốt nhất với giá cả hợp lý.",
    video_id: "dQw4w9WgXcQ",
    image_url:
      "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    image_alt: "Showroom nội thất",
    stats: {
      years_experience: 13,
      projects_completed: 2500,
      happy_customers: 1800,
      awards: 15,
    },
    features: [
      "Thiết kế 2D/3D chuyên nghiệp",
      "Thi công trọn gói",
      "Bảo hành dài hạn",
      "Tư vấn miễn phí",
    ],
    is_active: true,
  },
};

// Mock Du An Noi That Data
export const mockDuAnNoiThatData: DuAnNoiThatData = {
  projects: [
    {
      id: 1,
      title: "Căn hộ chung cư Vinhomes",
      description: "Thiết kế nội thất căn hộ 3 phòng ngủ phong cách hiện đại",
      category: "Chung cư",
      image_url:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      image_alt: "Căn hộ Vinhomes",
      gallery: [
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      ],
      area: 85,
      location: "Quận 9, TP.HCM",
      completion_date: "2024-03-15",
      budget_range: "200-300 triệu",
      style: "Hiện đại",
      order: 1,
      is_featured: true,
      is_active: true,
    },
    {
      id: 2,
      title: "Biệt thự Thảo Điền",
      description:
        "Thiết kế nội thất biệt thự sang trọng phong cách tân cổ điển",
      category: "Biệt thự",
      image_url:
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      image_alt: "Biệt thự Thảo Điền",
      gallery: [],
      area: 250,
      location: "Quận 2, TP.HCM",
      completion_date: "2024-02-20",
      budget_range: "800-1000 triệu",
      style: "Tân cổ điển",
      order: 2,
      is_featured: true,
      is_active: true,
    },
  ],
  categories: ["Chung cư", "Biệt thự", "Nhà phố", "Văn phòng"],
};

// Mock San Pham Va Danh Muc Data
export const mockSanPhamVaDanhMucData: SanPhamVaDanhMucData = {
  products: [
    {
      id: 1,
      name: "Sofa da thật Italia",
      description: "Sofa da thật nhập khẩu từ Italia, thiết kế sang trọng",
      category_id: 1,
      category_name: "Sofa",
      price: 25000000,
      price_range: "20-30 triệu",
      image_url:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      image_alt: "Sofa da Italia",
      gallery: [],
      specifications: {
        material: "Da thật",
        origin: "Italia",
        warranty: "5 năm",
      },
      is_featured: true,
      is_active: true,
      order: 1,
    },
    {
      id: 2,
      name: "Bàn ăn gỗ sồi",
      description: "Bàn ăn gỗ sồi tự nhiên cho 6 người",
      category_id: 2,
      category_name: "Bàn ăn",
      price: 8500000,
      price_range: "8-10 triệu",
      image_url:
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      image_alt: "Bàn ăn gỗ sồi",
      gallery: [],
      specifications: {
        material: "Gỗ sồi",
        size: "160x80cm",
        warranty: "2 năm",
      },
      is_featured: false,
      is_active: true,
      order: 2,
    },
  ],
  categories: [
    {
      id: 1,
      name: "Sofa",
      description: "Các loại sofa cao cấp",
      image_url:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      image_alt: "Danh mục sofa",
      parent_id: undefined,
      order: 1,
      is_active: true,
      product_count: 15,
    },
    {
      id: 2,
      name: "Bàn ăn",
      description: "Bàn ăn các loại",
      image_url:
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      image_alt: "Danh mục bàn ăn",
      parent_id: undefined,
      order: 2,
      is_active: true,
      product_count: 8,
    },
  ],
};

// Mock A13Nam Data
export const mockA13NamData: A13NamData = {
  company_info: {
    id: 1,
    company_name: "Nội Thất A13",
    years_experience: 13,
    description:
      "Công ty thiết kế nội thất hàng đầu với 13 năm kinh nghiệm, chuyên tạo ra những không gian sống đẹp và tiện nghi.",
    achievements: [
      "Top 10 công ty thiết kế nội thất uy tín nhất Việt Nam",
      "Hơn 2500 dự án hoàn thành thành công",
      "Đối tác chiến lược của các thương hiệu nội thất quốc tế",
      "Giải thưởng thiết kế xuất sắc năm 2023",
    ],
    certifications: [
      "ISO 9001:2015",
      "Giấy phép kinh doanh",
      "Chứng nhận năng lực thi công",
      "Chứng nhận đối tác ủy quyền",
    ],
    team_size: 50,
    projects_completed: 2500,
    awards: [
      "Giải vàng thiết kế nội thất 2023",
      "Top Brand Award 2022",
      "Customer Choice Award 2021",
    ],
    image_url:
      "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    image_alt: "Văn phòng công ty A13",
  },
};

// Mock Media Data (TTTT)
export const mockMediaData: MediaApiResponse = {
  success: true,
  data: {
    video_section: [
      {
        id: 1,
        title: "Video giới thiệu công ty",
        type: "youtube",
        section: "video",
        embed_code:
          '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
        media_url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        media_id: "dQw4w9WgXcQ",
        thumbnail_url:
          "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
        thumbnail_alt: "Video giới thiệu",
        thu_tu: 1,
        is_youtube: true,
        is_tiktok: false,
      },
      {
        id: 2,
        title: "Quy trình thiết kế nội thất",
        type: "youtube",
        section: "video",
        embed_code:
          '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
        media_url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        media_id: "dQw4w9WgXcQ",
        thumbnail_url:
          "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
        thumbnail_alt: "Quy trình thiết kế",
        thu_tu: 2,
        is_youtube: true,
        is_tiktok: false,
      },
    ],
    tiktok_section: [
      {
        id: 3,
        title: "Tips thiết kế phòng khách",
        type: "tiktok",
        section: "tiktok",
        embed_code:
          '<blockquote class="tiktok-embed" cite="https://www.tiktok.com/@username/video/1234567890"><section></section></blockquote>',
        media_url: "https://www.tiktok.com/@username/video/1234567890",
        media_id: "1234567890",
        thumbnail_url: null,
        thumbnail_alt: null,
        thu_tu: 1,
        is_youtube: false,
        is_tiktok: true,
      },
    ],
  },
  meta: {
    total_videos: 2,
    total_tiktoks: 1,
    total_items: 3,
    generated_at: "2024-01-01T00:00:00Z",
  },
};

// Mock He Sinh Thai Data
export const mockHeSinhThaiData: HeSinhThaiData = {
  ecosystem: {
    id: 1,
    title: "Hệ sinh thái nội thất toàn diện",
    description:
      "Chúng tôi cung cấp giải pháp nội thất toàn diện từ thiết kế, sản xuất đến thi công và bảo hành.",
    services: [
      "Tư vấn thiết kế miễn phí",
      "Thiết kế 2D/3D chuyên nghiệp",
      "Sản xuất nội thất theo yêu cầu",
      "Thi công trọn gói",
      "Bảo hành và bảo trì",
    ],
    process_steps: [
      {
        step: 1,
        title: "Tư vấn & Khảo sát",
        description: "Tư vấn miễn phí và khảo sát thực tế không gian",
        icon: "fa-search",
      },
      {
        step: 2,
        title: "Thiết kế",
        description: "Thiết kế 2D/3D theo yêu cầu khách hàng",
        icon: "fa-pencil-ruler",
      },
      {
        step: 3,
        title: "Sản xuất",
        description: "Sản xuất nội thất chất lượng cao",
        icon: "fa-hammer",
      },
      {
        step: 4,
        title: "Thi công",
        description: "Thi công và lắp đặt chuyên nghiệp",
        icon: "fa-tools",
      },
    ],
    benefits: [
      "Tiết kiệm thời gian và chi phí",
      "Chất lượng được đảm bảo",
      "Dịch vụ chuyên nghiệp",
      "Bảo hành dài hạn",
    ],
    image_url:
      "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    image_alt: "Hệ sinh thái nội thất",
  },
};

// Mock Doi Tac Data
export const mockDoiTacData: DoiTacData = {
  partners: [
    {
      id: 1,
      name: "IKEA Vietnam",
      logo_url: "https://via.placeholder.com/200x100/0066CC/FFFFFF?text=IKEA",
      logo_alt: "Logo IKEA",
      website_url: "https://ikea.com.vn",
      description: "Đối tác chiến lược cung cấp nội thất",
      partnership_type: "Nhà cung cấp",
      order: 1,
      is_active: true,
    },
    {
      id: 2,
      name: "Hafele Vietnam",
      logo_url: "https://via.placeholder.com/200x100/FF6600/FFFFFF?text=Hafele",
      logo_alt: "Logo Hafele",
      website_url: "https://hafele.com.vn",
      description: "Phụ kiện nội thất cao cấp",
      partnership_type: "Nhà cung cấp",
      order: 2,
      is_active: true,
    },
    {
      id: 3,
      name: "Blum Vietnam",
      logo_url: "https://via.placeholder.com/200x100/009900/FFFFFF?text=Blum",
      logo_alt: "Logo Blum",
      website_url: "https://blum.com",
      description: "Phụ kiện tủ bếp chất lượng cao",
      partnership_type: "Nhà cung cấp",
      order: 3,
      is_active: true,
    },
  ],
};

// Mock Nhan Biet Thuong Hieu Data
export const mockNbtctData: NbtctData = {
  brand_identity: {
    id: 1,
    title: "Nhận biết thương hiệu A13",
    description:
      "Thương hiệu nội thất uy tín với 13 năm kinh nghiệm, được khách hàng tin tưởng và lựa chọn.",
    brand_elements: {
      logo: "Logo A13 với màu xanh đặc trưng",
      colors: ["#0066CC", "#FF6600", "#009900"],
      fonts: ["Roboto", "Open Sans"],
      values: ["Chất lượng", "Uy tín", "Sáng tạo", "Chuyên nghiệp"],
    },
    unique_selling_points: [
      "13 năm kinh nghiệm trong ngành",
      "Hơn 2500 dự án thành công",
      "Đội ngũ thiết kế chuyên nghiệp",
      "Cam kết chất lượng và tiến độ",
      "Giá cả cạnh tranh, minh bạch",
    ],
    image_url:
      "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    image_alt: "Thương hiệu A13",
  },
};

// Export all mock data as a combined object
export const mockHomepageData = {
  banners: mockBannerData,
  gioithieu: mockGioiThieuData,
  duannoithat: mockDuAnNoiThatData,
  sanphamvadanhmuc: mockSanPhamVaDanhMucData,
  a13nam: mockA13NamData,
  tttt: mockMediaData,
  hesinhthai: mockHeSinhThaiData,
  doitac: mockDoiTacData,
  nbtct: mockNbtctData,
};
