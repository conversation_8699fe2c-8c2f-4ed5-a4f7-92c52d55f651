---
import "../css/hesinhthai.css";
import type { HeSinhThaiData } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: HeSinhThaiData;
}

const { data } = Astro.props;

// Use data from props
const ecosystemData = data?.ecosystem;
---

{ecosystemData && (
  <section class="ecosystem-section">
    <div class="ecosystem-container">
      <div class="ecosystem-header">
        <h2 class="ecosystem-title">{ecosystemData.title || "HỆ SINH THÁI"}</h2>
      </div>

      <div class="ecosystem-description">
        <p>{ecosystemData.description}</p>
      </div>

      {/* Services Grid */}
      {ecosystemData.services && ecosystemData.services.length > 0 && (
        <div class="ecosystem-services-grid">
          {ecosystemData.services.map((service, index) => (
            <div class="ecosystem-service-card" data-aos="fade-up" data-aos-delay={`${(index + 1) * 100}`}>
              <div class="service-content">
                <div class="service-icon">
                  <i class="fas fa-cogs"></i>
                </div>
                <h3 class="service-title">{service}</h3>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Process Steps */}
      {ecosystemData.process_steps && ecosystemData.process_steps.length > 0 && (
        <div class="process-section">
          <h3>Quy trình làm việc</h3>
          <div class="process-steps-grid">
            {ecosystemData.process_steps.map((step) => (
              <div class="process-step">
                <div class="step-number">{step.step}</div>
                <div class="step-content">
                  {step.icon && <i class={step.icon}></i>}
                  <h4>{step.title}</h4>
                  <p>{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Benefits */}
      {ecosystemData.benefits && ecosystemData.benefits.length > 0 && (
        <div class="benefits-section">
          <h3>Lợi ích</h3>
          <div class="benefits-grid">
            {ecosystemData.benefits.map((benefit) => (
              <div class="benefit-item">
                <i class="fas fa-check-circle"></i>
                <span>{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Ecosystem Image */}
      {ecosystemData.image_url && (
        <div class="ecosystem-image">
          <img 
            src={ecosystemData.image_url}
            alt={ecosystemData.image_alt || "Hệ sinh thái"}
            class="ecosystem-img"
          />
        </div>
      )}
    </div>
  </section>
)}

<script src="/js/hesinhthai.js" defer is:inline></script>

<style>
  .ecosystem-description {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 1.1rem;
    color: #64748b;
  }

  .ecosystem-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .ecosystem-service-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .ecosystem-service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .service-icon {
    width: 60px;
    height: 60px;
    background: #1e40af;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
  }

  .service-icon i {
    color: white;
    font-size: 1.5rem;
  }

  .service-title {
    color: #1e40af;
    margin-bottom: 1rem;
  }

  .process-section {
    margin: 3rem 0;
  }

  .process-section h3 {
    text-align: center;
    color: #1e40af;
    margin-bottom: 2rem;
  }

  .process-steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
  }

  .process-step {
    text-align: center;
    position: relative;
  }

  .step-number {
    width: 50px;
    height: 50px;
    background: #1e40af;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    margin: 0 auto 1rem;
  }

  .step-content h4 {
    color: #1e40af;
    margin-bottom: 0.5rem;
  }

  .step-content p {
    color: #64748b;
    font-size: 0.9rem;
  }

  .benefits-section {
    margin: 3rem 0;
  }

  .benefits-section h3 {
    text-align: center;
    color: #1e40af;
    margin-bottom: 2rem;
  }

  .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }

  .benefit-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #10b981;
  }

  .benefit-item i {
    color: #10b981;
    font-size: 1.25rem;
  }

  .ecosystem-image {
    text-align: center;
    margin-top: 3rem;
  }

  .ecosystem-img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Responsive */
  @media (max-width: 768px) {
    .ecosystem-services-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .process-steps-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .benefits-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
