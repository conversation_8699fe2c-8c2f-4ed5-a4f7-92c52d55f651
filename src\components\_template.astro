---
// Template for creating new components with props
import "../css/[COMPONENT_NAME].css";
import type { [DATA_TYPE] } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: [DATA_TYPE];
}

const { data } = Astro.props;

// Use data from props or provide fallback
const componentData = data?.[DATA_PROPERTY] || null;
---

<section class="[COMPONENT_NAME]-section">
  {componentData ? (
    <!-- Render with API data -->
    <div class="[COMPONENT_NAME]-container">
      <h2>{componentData.title}</h2>
      <p>{componentData.description}</p>
      <!-- Add more content based on your data structure -->
    </div>
  ) : (
    <!-- Fallback content when no data available -->
    <div class="[COMPONENT_NAME]-container">
      <h2>Đang tải...</h2>
      <p>Nội dung đang được tải...</p>
    </div>
  )}
</section>

<script src="/js/[COMPONENT_NAME].js" defer is:inline></script>

<!-- 
INSTRUCTIONS FOR USING THIS TEMPLATE:

1. Replace [COMPONENT_NAME] with your component name (e.g., "duannoithat")
2. Replace [DATA_TYPE] with the appropriate data type from types/index.ts (e.g., "DuAnNoiThatData")
3. Replace [DATA_PROPERTY] with the property name from the data type (e.g., "projects")
4. Update the CSS import path
5. Update the JavaScript file path
6. Customize the HTML structure based on your component needs
7. Add proper TypeScript types for your specific data structure

Example for DuAnNoiThat component:
- [COMPONENT_NAME] → duannoithat
- [DATA_TYPE] → DuAnNoiThatData  
- [DATA_PROPERTY] → projects

Example for SanPhamVaDanhMuc component:
- [COMPONENT_NAME] → sanphamvadanhmuc
- [DATA_TYPE] → SanPhamVaDanhMucData
- [DATA_PROPERTY] → products (or categories)
-->
