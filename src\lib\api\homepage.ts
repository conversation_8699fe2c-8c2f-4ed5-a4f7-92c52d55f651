import type {
  ApiResponse,
  HomepageData,
  BannerData,
  GioiThieuData,
  DuAnNoiThatData,
  SanPhamVaDanhMucData,
  A13NamData,
  MediaApiResponse,
  HeSinhThaiData,
  DoiTacData,
  NbtctData,
  API_ENDPOINTS,
} from "../../types/index";

// API Base Configuration
const API_BASE_URL = "http://127.0.0.1:8000";

// Generic fetch function with error handling
async function fetchApi<T>(endpoint: string): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error);
    throw error;
  }
}

// Individual API fetch functions
export async function fetchBanners(): Promise<ApiResponse<BannerData>> {
  return fetchApi<BannerData>("/api/banners");
}

export async function fetchGioiThieu(): Promise<ApiResponse<GioiThieuData>> {
  return fetchApi<GioiThieuData>("/api/gioithieu");
}

export async function fetchDuAnNoiThat(): Promise<
  ApiResponse<DuAnNoiThatData>
> {
  return fetchApi<DuAnNoiThatData>("/api/duannoithat");
}

export async function fetchSanPhamVaDanhMuc(): Promise<
  ApiResponse<SanPhamVaDanhMucData>
> {
  return fetchApi<SanPhamVaDanhMucData>("/api/sanphamvadanhmuc");
}

export async function fetchA13Nam(): Promise<ApiResponse<A13NamData>> {
  return fetchApi<A13NamData>("/api/a13nam");
}

export async function fetchMediaContents(): Promise<MediaApiResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/media-contents`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data as MediaApiResponse;
  } catch (error) {
    console.error(`Error fetching media contents:`, error);
    throw error;
  }
}

export async function fetchHeSinhThai(): Promise<ApiResponse<HeSinhThaiData>> {
  return fetchApi<HeSinhThaiData>("/api/hesinhthai");
}

export async function fetchDoiTac(): Promise<ApiResponse<DoiTacData>> {
  return fetchApi<DoiTacData>("/api/doitac");
}

export async function fetchNbtct(): Promise<ApiResponse<NbtctData>> {
  return fetchApi<NbtctData>("/api/nbtct");
}

// Homepage API Response type - different from HomepageData
export interface HomepageApiResponse {
  banners?: ApiResponse<BannerData> | null;
  gioithieu?: ApiResponse<GioiThieuData> | null;
  duannoithat?: ApiResponse<DuAnNoiThatData> | null;
  sanphamvadanhmuc?: ApiResponse<SanPhamVaDanhMucData> | null;
  a13nam?: ApiResponse<A13NamData> | null;
  tttt?: MediaApiResponse | null;
  hesinhthai?: ApiResponse<HeSinhThaiData> | null;
  doitac?: ApiResponse<DoiTacData> | null;
  nbtct?: ApiResponse<NbtctData> | null;
}

// Main function to fetch all homepage data
export async function fetchHomepageData(): Promise<HomepageApiResponse> {
  const results: HomepageApiResponse = {};

  // Fetch all data concurrently with error handling for each
  const fetchPromises = [
    fetchBanners()
      .then((data) => ({ banners: data }))
      .catch((err) => {
        console.error("Failed to fetch banners:", err);
        return { banners: null };
      }),

    fetchGioiThieu()
      .then((data) => ({ gioithieu: data }))
      .catch((err) => {
        console.error("Failed to fetch gioithieu:", err);
        return { gioithieu: null };
      }),

    fetchDuAnNoiThat()
      .then((data) => ({ duannoithat: data }))
      .catch((err) => {
        console.error("Failed to fetch duannoithat:", err);
        return { duannoithat: null };
      }),

    fetchSanPhamVaDanhMuc()
      .then((data) => ({ sanphamvadanhmuc: data }))
      .catch((err) => {
        console.error("Failed to fetch sanphamvadanhmuc:", err);
        return { sanphamvadanhmuc: null };
      }),

    fetchA13Nam()
      .then((data) => ({ a13nam: data }))
      .catch((err) => {
        console.error("Failed to fetch a13nam:", err);
        return { a13nam: null };
      }),

    fetchMediaContents()
      .then((data) => ({ tttt: data }))
      .catch((err) => {
        console.error("Failed to fetch media contents:", err);
        return { tttt: null };
      }),

    fetchHeSinhThai()
      .then((data) => ({ hesinhthai: data }))
      .catch((err) => {
        console.error("Failed to fetch hesinhthai:", err);
        return { hesinhthai: null };
      }),

    fetchDoiTac()
      .then((data) => ({ doitac: data }))
      .catch((err) => {
        console.error("Failed to fetch doitac:", err);
        return { doitac: null };
      }),

    fetchNbtct()
      .then((data) => ({ nbtct: data }))
      .catch((err) => {
        console.error("Failed to fetch nbtct:", err);
        return { nbtct: null };
      }),
  ];

  // Wait for all promises to resolve
  const responses = await Promise.all(fetchPromises);

  // Merge all responses into results object
  responses.forEach((response) => {
    Object.assign(results, response);
  });

  return results;
}

// Utility function to get fallback data for failed API calls
export function getFallbackData() {
  return {
    banners: {
      success: false,
      data: { banners: [] },
    },
    gioithieu: {
      success: false,
      data: { intro: null },
    },
    duannoithat: {
      success: false,
      data: { projects: [], categories: [] },
    },
    sanphamvadanhmuc: {
      success: false,
      data: { products: [], categories: [] },
    },
    a13nam: {
      success: false,
      data: { company_info: null },
    },
    tttt: {
      success: false,
      data: { video_section: [], tiktok_section: [] },
      meta: {
        total_videos: 0,
        total_tiktoks: 0,
        total_items: 0,
        generated_at: "",
      },
    },
    hesinhthai: {
      success: false,
      data: {
        ecosystem: {
          id: 0,
          title: "",
          description: "",
          services: [],
          process_steps: [],
          benefits: [],
          image_url: "",
          image_alt: "",
        },
      },
    },
    doitac: {
      success: false,
      data: { partners: [] },
    },
    nbtct: {
      success: false,
      data: {
        brand_identity: {
          id: 0,
          title: "",
          description: "",
          brand_elements: {
            logo: "",
            colors: [],
            fonts: [],
            values: [],
          },
          unique_selling_points: [],
          image_url: "",
          image_alt: "",
        },
      },
    },
  };
}

// Helper function to check if data is valid
export function isValidData<T>(
  apiResponse: ApiResponse<T> | null | undefined
): boolean {
  return (
    apiResponse !== null &&
    apiResponse !== undefined &&
    apiResponse.success === true &&
    apiResponse.data !== null
  );
}

// Helper function to get data or fallback
export function getDataOrFallback<T>(
  apiResponse: ApiResponse<T> | null | undefined,
  fallback: T
): T {
  if (isValidData(apiResponse)) {
    return apiResponse!.data;
  }
  return fallback;
}

// Special helper for MediaApiResponse
export function getMediaDataOrFallback(
  apiResponse: MediaApiResponse | null | undefined,
  fallback: any
): any {
  if (
    apiResponse !== null &&
    apiResponse !== undefined &&
    apiResponse.success === true &&
    apiResponse.data !== null
  ) {
    return apiResponse.data;
  }
  return fallback;
}
