/* Introduction section */
.intro-section {
    padding: 24px 0;
}

.intro-title {
    text-align: center;
    font-size: 28px;
    font-weight: bold;
    color: black;
}

.intro-bg {
    margin: 0 auto;
    height: auto;
    min-height: 400px;
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.intro-bg-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.intro-bg-video iframe {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100vw;
    height: 56.25vw;
    /* 16:9 aspect ratio */
    min-width: 100%;
    min-height: 100%;
    transform: translate(-50%, -50%);
    border: none;
    object-fit: cover;
}

/* Ensure video covers container on all screen sizes */
@media (max-aspect-ratio: 16/9) {
    .intro-bg-video iframe {
        width: 177.78vh;
        /* 16:9 aspect ratio */
        height: 100vh;
    }
}

.intro-content {
    display: flex;
    height: 100%;
    flex-direction: column;
    margin: 0 80px;
    align-items: center;
    justify-content: space-between;
    padding: 16px 40px;
    color: white;
}

.intro-left {
    width: 100%;
    padding-right: 0;
    margin-bottom: 24px;
}

.intro-subtitle>h1 {
    text-align: center;
}

.accordion-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    width: 100%;
    max-width: 672px;
    border-radius: 20px;
    background-color: #000000b8;
    padding: 12px 16px;
    color: white;
    box-shadow: 4px 5px 0px rgb(0 0 0 / 30%);
    margin-top: 16px;
}

.accordion-container:hover {
    transform: translateY(-4px);
    /* Di chuyển nhẹ lên trên */
    transition: all 0.3s ease;
    box-shadow: 4px 8px 0px rgb(0 0 0 / 30%);
}


.accordion-text {
    font-size: 14px;
    text-align: center;
    line-height: 1.6;
}

.accordion-extra {
    margin-top: 16px;
    display: none;
}

.accordion-extra.open {
    display: block;
}

.accordion-button {
    margin-top: 12px;
    display: inline-block;
    border-radius: 20px;
    background-color: #e5e7eb;
    padding: 4px 12px;
    font-size: 12px;
    color: #374151;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
}

.accordion-button:hover {
    background-color: #d1d5db;
}

.intro-right {
    width: 100%;
    padding-left: 0;
}

.map-container {
    /* position: relative; */
    display: flex;
    flex-direction: column;
    align-items: center;
}

.map-img-wrapper {
    position: relative;
    margin: auto;
}

.map-img {
    margin: 0 auto;
    max-width: 100%;
    height: auto;
}

.location-dropdown {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
}

.location-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border: 1px solid #d1d5db;
    border-radius: 9999px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    color: #1f2937;
    font-weight: 500;
    font-size: 12px;
    cursor: pointer;
    transition: box-shadow 0.2s;
    min-width: 120px;
    justify-content: space-between;
}

.location-button:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.location-icon {
    color: #dc2626;
    font-size: 16px;
    padding-right: 8px;
}

.dropdown-menu {
    position: absolute;
    top: 36px;
    margin-top: 4px;
    background-color: white;
    color: black;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 144px;
    padding: 8px;
    z-index: 50;
    font-size: 12px;
    display: none;
}

.dropdown-menu.open {
    display: block;
}

.dropdown-item {
    padding: 4px 8px;
    cursor: pointer;
    margin: 4px 0;
    border-radius: 4px;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
}

/* Introduction section responsive design */
@media (max-width: 1024px) {
    .intro-content {
        margin: 0 40px;
        padding: 16px 20px;
    }

    .intro-title {
        font-size: 32px;
    }

    .intro-subtitle {
        font-size: 22px;
    }

    .accordion-text {
        font-size: 15px;
    }

    .intro-bg {
        min-height: 350px;
    }
}

@media (min-width: 768px) {
    .intro-title {
        font-size: 37px;
    }

    .intro-bg {
        min-height: 600px;
    }

    .intro-content {
        flex-direction: row;
    }

    .intro-left {
        width: 50%;
        padding-right: 24px;
        margin-bottom: 0;
    }

    .intro-subtitle {
        font-size: 24px;
        text-align: left;
    }

    .accordion-container {
        padding: 16px;
        max-width: none;
    }

    .accordion-text {
        font-size: 16px;
        text-align: left;
    }

    .accordion-button {
        margin-top: 16px;
        padding: 8px 16px;
        font-size: 14px;
    }

    .intro-right {
        width: 100%;
        padding-left: 24px;
    }

    .map-container {
        align-items: flex-start;
    }

    .location-dropdown {
        position: absolute;
        justify-content: flex-start;
        margin-top: 0;
        top: 77%;
        right: 5%;
    }


    .location-button {
        padding: 8px 16px;
        font-size: 14px;
        min-width: 160px;
    }

    .dropdown-menu {
        width: 192px;
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .intro-section {
        padding: 16px 0;
    }

    .intro-title {
        font-size: 24px;
        margin: 8px 0 16px 0;
    }

    .intro-bg {
        border-radius: 6px;
        margin: 0 10px;
        min-height: 300px;
    }

    .intro-content {
        margin: 0 20px;
        padding: 16px 20px;
        flex-direction: column;
    }

    .intro-left {
        width: 100%;
        padding-right: 0;
        margin-bottom: 20px;
    }

    .intro-subtitle {
        font-size: 18px;
        text-align: center;
    }

    .accordion-container {
        padding: 12px;
        max-width: 100%;
    }

    .accordion-text {
        font-size: 14px;
        text-align: center;
    }

    .accordion-button {
        padding: 6px 12px;
        font-size: 12px;
    }

    .intro-right {
        width: 100%;
        padding-left: 0;
    }

    .map-container {
        align-items: center;
    }

    .map-img {
        max-width: 100%;
    }

    .location-dropdown {
        position: absolute;
        justify-content: center;
        margin-top: 12px;
        top: 74%;
        left: 38%;
    }

    .location-button {
        padding: 6px 12px;
        font-size: 12px;
        min-width: 140px;
    }

    .dropdown-menu {
        width: 160px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .intro-title {
        font-size: 20px;
        margin: 6px 0 12px 0;
    }

    .intro-bg {
        margin: 0 5px;
        border-radius: 4px;
        min-height: 250px;
    }

    .intro-content {
        margin: 0 10px;
        padding: 12px 15px;
    }

    .intro-subtitle {
        font-size: 16px;
    }

    .intro-subtitle h1 {
        margin: 0;
        font-size: 16px;
    }

    .accordion-container {
        padding: 10px;
    }

    .accordion-text {
        font-size: 13px;
        line-height: 1.5;
    }

    .accordion-button {
        padding: 5px 10px;
        font-size: 11px;
        margin-top: 8px;
    }

    .map-img {
        max-width: 90%;
    }

    .location-dropdown {
        margin-top: 10px;
        position: absolute;
        top: 73%;
        left: 34%;
    }

    .location-button {
        padding: 5px 10px;
        font-size: 11px;
        min-width: 120px;
    }

    .location-icon {
        font-size: 14px;
        padding-right: 6px;
    }

    .dropdown-menu {
        width: 140px;
        font-size: 11px;
        padding: 6px;
    }

    .dropdown-item {
        padding: 3px 6px;
        margin: 2px 0;
    }
}

@media (max-width: 320px) {
    .intro-title {
        font-size: 18px;
    }

    .intro-content {
        margin: 0 5px;
        padding: 10px;
    }

    .intro-subtitle {
        font-size: 14px;
    }

    .intro-subtitle h1 {
        font-size: 14px;
    }

    .accordion-text {
        font-size: 12px;
    }

    .accordion-button {
        padding: 4px 8px;
        font-size: 10px;
    }

    .location-button {
        padding: 4px 8px;
        font-size: 10px;
        min-width: 100px;
    }

    .dropdown-menu {
        width: 120px;
        font-size: 10px;
    }
}