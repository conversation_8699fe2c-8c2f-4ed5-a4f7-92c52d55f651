.banner-container {
    position: relative;
    width: 100%;
    height: 60vh;
    min-height: 500px;
    /* <PERSON><PERSON> chuyển background-image vào inline style trong component */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.3s ease;
}

.banner-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.1), rgba(160, 82, 45, 0.1));
    animation: shimmer 3s ease-in-out infinite alternate;
}

@keyframes shimmer {
    0% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.7;
    }
}

.banner-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 40px 20px;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.banner-content h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
    animation: slideInLeft 1s ease-out 0.3s both;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.banner-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    margin-bottom: 30px;
    animation: slideInRight 1s ease-out 0.6s both;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hiệu ứng hover cho toàn bộ banner - đã sửa */
.banner-container:hover {
    background-size: 110%;
    transition: all 0.3s ease;
}

.banner-container:hover::before {
    animation-duration: 1.5s;
}

/* Responsive design */
@media (max-width: 768px) {
    .banner-container {
        height: 50vh;
        min-height: 400px;
    }
    
    .banner-content h1 {
        font-size: 1.8rem;
    }
    
    .banner-content p {
        font-size: 1rem;
    }
    
    .banner-content {
        padding: 20px 15px;
    }
}

@media (max-width: 480px) {
    .banner-container {
        height: 45vh;
        min-height: 350px;
    }
    
    .banner-content h1 {
        font-size: 1.5rem;
    }
    
    .banner-content p {
        font-size: 0.9rem;
    }
}

/* Hiệu ứng parallax nhẹ */
.banner-container {
    background-attachment: fixed;
}

@media (max-width: 768px) {
    .banner-container {
        background-attachment: scroll;
    }
}
