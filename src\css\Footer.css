* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

/* Footer Section */
.footer-section {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: #ffffff;
    padding: clamp(48px, 8vw, 64px) 0 clamp(24px, 4vw, 32px);
    position: relative;
    overflow: hidden;
}

.footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="40" cy="80" r="0.8" fill="rgba(255,255,255,0.04)"/></svg>');
    pointer-events: none;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 clamp(16px, 4vw, 32px);
    position: relative;
    z-index: 1;
}

/* Footer Grid */
.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: clamp(24px, 6vw, 40px);
    margin-bottom: clamp(32px, 6vw, 48px);
}

/* Footer Column */
.footer-column {
    display: flex;
    flex-direction: column;
    gap: clamp(16px, 3vw, 20px);
}

.footer-title {
    font-size: clamp(16px, 2.5vw, 18px);
    font-weight: 700;
    color: #ffffff;
    margin-bottom: clamp(8px, 2vw, 12px);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #10b981);
    border-radius: 1px;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: clamp(8px, 1.5vw, 12px);
}

.footer-content p {
    font-size: clamp(13px, 1.8vw, 14px);
    color: #d1d5db;
    line-height: 1.6;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-content i {
    color: #3b82f6;
    width: 16px;
    flex-shrink: 0;
}

.footer-link {
    color: #d1d5db;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.footer-link:hover {
    color: #3b82f6;
    transform: translateX(4px);
}

.footer-link::before {
    content: '▶';
    position: absolute;
    left: -16px;
    color: #3b82f6;
    font-size: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.footer-link:hover::before {
    opacity: 1;
}

/* Registration Form */
.registration-form {
    display: flex;
    flex-direction: column;
    gap: clamp(12px, 2vw, 16px);
}

.form-input {
    width: 100%;
    padding: clamp(10px, 2vw, 12px) clamp(12px, 2.5vw, 16px);
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.6);
    border-radius: 8px;
    color: #ffffff;
    font-size: clamp(13px, 1.8vw, 14px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: #9ca3af;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(55, 65, 81, 1);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-button {
    width: 100%;
    padding: clamp(12px, 2.5vw, 14px) clamp(16px, 3vw, 20px);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-size: clamp(13px, 1.8vw, 14px);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.form-button i {
    font-size: 12px;
}

/* QR Section */
.qr-section {
    text-align: center;
    margin: clamp(16px, 3vw, 24px) 0;
}

.qr-title {
    font-weight: 600;
    margin-bottom: clamp(12px, 2vw, 16px);
    color: #ffffff;
    font-size: clamp(13px, 1.8vw, 14px);
}

.qr-code {
    width: clamp(100px, 15vw, 128px);
    height: clamp(100px, 15vw, 128px);
    background: #ffffff;
    margin: 0 auto;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.qr-code:hover {
    transform: scale(1.05);
}

.qr-code img {
    width: clamp(40px, 6vw, 50px);
    height: clamp(40px, 6vw, 50px);
}

.qr-code p {
    color: #374151;
    font-size: clamp(10px, 1.5vw, 12px);
    font-weight: 500;
    margin: 0;
}

/* Working Hours */
.working-hours {
    text-align: center;
    margin-top: clamp(16px, 3vw, 24px);
}

.hours-title {
    font-weight: 700;
    color: #ffffff;
    margin-bottom: clamp(8px, 1.5vw, 12px);
    font-size: clamp(13px, 1.8vw, 14px);
}

.working-hours p:not(.hours-title) {
    font-size: clamp(12px, 1.6vw, 13px);
    color: #d1d5db;
    margin-bottom: 4px;
}

/* Footer Image Styling */
.footer-column img {
    width: 100%;
    max-width: clamp(120px, 15vw, 180px);
    height: auto;
    object-fit: contain;
    border-radius: 8px;
    margin: clamp(12px, 2vw, 16px) auto;
    display: block;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.footer-column img:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(75, 85, 99, 0.3);
    padding-top: clamp(20px, 4vw, 32px);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(16px, 3vw, 24px);
}

/* Social Links */
.social-links {
    display: flex;
    gap: clamp(12px, 2vw, 16px);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: clamp(40px, 6vw, 48px);
    height: clamp(40px, 6vw, 48px);
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.6);
    border-radius: 50%;
    color: #d1d5db;
    font-size: clamp(16px, 2.5vw, 20px);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.social-link:hover.social-link:nth-child(1) {
    background: #1877f2;
    border-color: #1877f2;
    color: #ffffff;
}

.social-link:hover.social-link:nth-child(2) {
    background: #ff0000;
    border-color: #ff0000;
    color: #ffffff;
}

.social-link:hover.social-link:nth-child(3) {
    background: #000000;
    border-color: #000000;
    color: #ffffff;
}

.social-link:hover.social-link:nth-child(4) {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    border-color: transparent;
    color: #ffffff;
}

/* Copyright */
.copyright {
    color: #9ca3af;
    font-size: clamp(12px, 1.6vw, 14px);
    text-align: center;
}

/* Responsive Design */
@media (min-width: 1024px) {
    .footer-grid {
        grid-template-columns: 2fr 1.5fr 1.5fr 1.8fr;
    }

    .footer-bottom {
        flex-direction: row;
        justify-content: space-between;
    }

    .footer-column img {
        max-width: clamp(150px, 12vw, 200px);
    }
}

@media (max-width: 768px) {
    .footer-grid {
        grid-template-columns: 1fr;
        gap: clamp(32px, 6vw, 40px);
    }

    .footer-column {
        text-align: center;
    }

    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .qr-section,
    .working-hours {
        margin: clamp(20px, 4vw, 24px) 0;
    }

    .footer-column img {
        max-width: clamp(100px, 20vw, 150px);
        margin: clamp(10px, 2vw, 16px) auto;
    }
}

@media (max-width: 480px) {
    .footer-section {
        padding: clamp(32px, 6vw, 48px) 0 clamp(16px, 3vw, 24px);
    }

    .footer-container {
        padding: 0 clamp(12px, 3vw, 20px);
    }

    .social-links {
        gap: clamp(8px, 1.5vw, 12px);
    }

    .footer-column img {
        max-width: clamp(80px, 25vw, 120px);
        margin: clamp(8px, 1.5vw, 12px) auto;
    }
}

/* Animation */
.footer-column {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.footer-column:nth-child(1) {
    animation-delay: 0.1s;
}

.footer-column:nth-child(2) {
    animation-delay: 0.2s;
}

.footer-column:nth-child(3) {
    animation-delay: 0.3s;
}

.footer-column:nth-child(4) {
    animation-delay: 0.4s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

/* Print styles */
@media print {
    .footer-section {
        background: white;
        color: black;
        padding: 20px 0;
    }

    .social-links,
    .qr-code {
        display: none;
    }
}