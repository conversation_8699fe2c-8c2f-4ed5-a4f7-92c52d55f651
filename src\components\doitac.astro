---
import "../css/doitac.css";
import type { DoiTacData } from "../types/index";

// Receive data from parent component
export interface Props {
  data?: DoiTacData;
}

const { data } = Astro.props;

// Use data from props
const partners = data?.partners || [];
---

<section class="partners-section">
  <div class="partners-container">
    <div class="partners-header">
      <h2 class="partners-title">ĐỐI TÁC CỦA CHÚNG TÔI</h2>
      <p class="partners-subtitle">
        Hợp tác cùng những thương hiệu uy tín trong ngành
      </p>
    </div>
    <div class="partners-grid">
      {
        partners.length > 0 ? (
          partners
            .filter((partner) => partner.is_active)
            .sort((a, b) => a.order - b.order)
            .map((partner) => (
              <div class="partner-item">
                <div class="partner-logo">
                  {partner.website_url ? (
                    <a
                      href={partner.website_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <img
                        src={partner.logo_url}
                        alt={partner.logo_alt || partner.name}
                        title={partner.name}
                      />
                    </a>
                  ) : (
                    <img
                      src={partner.logo_url}
                      alt={partner.logo_alt || partner.name}
                      title={partner.name}
                    />
                  )}
                </div>
                {partner.description && (
                  <div class="partner-description">
                    <p>{partner.description}</p>
                  </div>
                )}
              </div>
            ))
        ) : (
          <div class="no-partners">
            <p>Chưa có thông tin đối tác</p>
          </div>
        )
      }
    </div>
  </div>
</section>
